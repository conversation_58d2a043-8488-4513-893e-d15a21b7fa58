/**
 * 设备指纹生成工具
 * Device Fingerprint Generator for AirMonitor
 * 
 * 功能：
 * - 生成基于硬件信息的唯一设备指纹
 * - 用于License验证和设备绑定
 * - 支持多种硬件信息收集方式
 */

class DeviceFingerprint {
    /**
     * 生成设备指纹
     * @returns {string} 设备指纹字符串
     */
    static generate() {
        try {
            const components = [
                this.getScreenInfo(),
                this.getTimezoneInfo(),
                this.getLanguageInfo(),
                this.getPlatformInfo(),
                this.getCanvasFingerprint(),
                this.getWebGLFingerprint(),
                this.getAudioFingerprint(),
                this.getStorageInfo()
            ];

            const fingerprint = this.hashComponents(components);
            return this.formatFingerprint(fingerprint);
        } catch (error) {
            console.warn('设备指纹生成失败，使用备用方案:', error);
            return this.generateFallbackFingerprint();
        }
    }

    /**
     * 获取屏幕信息
     */
    static getScreenInfo() {
        return {
            width: screen.width,
            height: screen.height,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight
        };
    }

    /**
     * 获取时区信息
     */
    static getTimezoneInfo() {
        return {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset()
        };
    }

    /**
     * 获取语言信息
     */
    static getLanguageInfo() {
        return {
            language: navigator.language,
            languages: navigator.languages ? navigator.languages.join(',') : ''
        };
    }

    /**
     * 获取平台信息
     */
    static getPlatformInfo() {
        return {
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            vendor: navigator.vendor || '',
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack || '',
            hardwareConcurrency: navigator.hardwareConcurrency || 0
        };
    }

    /**
     * 获取Canvas指纹
     */
    static getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 200;
            canvas.height = 50;
            
            // 绘制文本
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('AirMonitor Device Fingerprint', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Canvas Fingerprint Test', 4, 35);
            
            // 绘制几何图形
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 25, 20, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();
            
            return canvas.toDataURL();
        } catch (error) {
            return 'canvas_error';
        }
    }

    /**
     * 获取WebGL指纹
     */
    static getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                return 'webgl_not_supported';
            }

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const vendor = debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : '';
            const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';
            
            return {
                vendor: vendor,
                renderer: renderer,
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)
            };
        } catch (error) {
            return 'webgl_error';
        }
    }

    /**
     * 获取音频指纹
     */
    static getAudioFingerprint() {
        try {
            if (!window.AudioContext && !window.webkitAudioContext) {
                return 'audio_not_supported';
            }

            const AudioContext = window.AudioContext || window.webkitAudioContext;
            const context = new AudioContext();
            
            const oscillator = context.createOscillator();
            const analyser = context.createAnalyser();
            const gain = context.createGain();
            const scriptProcessor = context.createScriptProcessor(4096, 1, 1);
            
            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(10000, context.currentTime);
            
            gain.gain.setValueAtTime(0, context.currentTime);
            
            oscillator.connect(analyser);
            analyser.connect(scriptProcessor);
            scriptProcessor.connect(gain);
            gain.connect(context.destination);
            
            oscillator.start(0);
            
            const audioData = new Float32Array(analyser.frequencyBinCount);
            analyser.getFloatFrequencyData(audioData);
            
            context.close();
            
            return Array.from(audioData.slice(0, 10)).join(',');
        } catch (error) {
            return 'audio_error';
        }
    }

    /**
     * 获取存储信息
     */
    static getStorageInfo() {
        try {
            return {
                localStorage: !!window.localStorage,
                sessionStorage: !!window.sessionStorage,
                indexedDB: !!window.indexedDB,
                webSQL: !!window.openDatabase
            };
        } catch (error) {
            return 'storage_error';
        }
    }

    /**
     * 对组件进行哈希处理
     */
    static hashComponents(components) {
        const str = JSON.stringify(components);
        let hash = 0;
        
        if (str.length === 0) return hash.toString();
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return Math.abs(hash).toString(16).toUpperCase();
    }

    /**
     * 格式化指纹为可读格式
     */
    static formatFingerprint(hash) {
        // 确保指纹长度至少为16位
        const paddedHash = hash.padStart(16, '0');
        
        // 格式化为 XXXX-XXXX-XXXX-XXXX 格式
        return paddedHash.match(/.{1,4}/g).join('-');
    }

    /**
     * 生成备用指纹（当主要方法失败时使用）
     */
    static generateFallbackFingerprint() {
        const timestamp = Date.now().toString(16);
        const random = Math.random().toString(16).substr(2, 8);
        const userAgent = navigator.userAgent.length.toString(16);
        const screen = (screen.width * screen.height).toString(16);
        
        const fallback = (timestamp + random + userAgent + screen).substr(0, 16);
        return this.formatFingerprint(fallback.toUpperCase());
    }

    /**
     * 验证指纹格式
     */
    static validateFormat(fingerprint) {
        const pattern = /^[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}$/;
        return pattern.test(fingerprint);
    }

    /**
     * 获取指纹详细信息（用于调试）
     */
    static getDetailedInfo() {
        return {
            screen: this.getScreenInfo(),
            timezone: this.getTimezoneInfo(),
            language: this.getLanguageInfo(),
            platform: this.getPlatformInfo(),
            storage: this.getStorageInfo(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 比较两个指纹是否匹配
     */
    static compare(fingerprint1, fingerprint2) {
        if (!fingerprint1 || !fingerprint2) {
            return false;
        }
        
        return fingerprint1.toUpperCase() === fingerprint2.toUpperCase();
    }

    /**
     * 生成指纹摘要（用于日志记录）
     */
    static getSummary(fingerprint) {
        if (!this.validateFormat(fingerprint)) {
            return 'Invalid fingerprint format';
        }
        
        const parts = fingerprint.split('-');
        return `${parts[0]}****${parts[3]}`;
    }
}

// 导出到全局作用域（用于HTML页面直接调用）
if (typeof window !== 'undefined') {
    window.DeviceFingerprint = DeviceFingerprint;
}

// 模块导出（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DeviceFingerprint;
}
