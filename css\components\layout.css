/**
 * 布局组件样式 - Layout Components
 * 专业工具软件布局系统
 * 支持1280×768最低分辨率
 */

/* ================================
   应用程序主布局
   ================================ */

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-width: 1280px;
  background-color: var(--bg-primary);
}

/* 顶部工具栏 */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--toolbar-height);
  padding: 0 var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-tertiary);
  box-shadow: var(--shadow-2);
  z-index: var(--z-sticky);
}

.app-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-header-center {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  justify-content: center;
}

.app-header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 应用Logo */
.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-body-large);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
}

.app-logo-icon {
  width: 24px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: var(--radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-on-primary);
  font-weight: var(--font-weight-bold);
}

/* 主内容区域 */
.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.app-sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-tertiary);
  display: flex;
  flex-direction: column;
  transition: width var(--duration-normal) var(--easing-standard);
  z-index: var(--z-fixed);
}

.app-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.app-sidebar-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.app-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm) 0;
}

.app-sidebar-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-tertiary);
}

/* 内容区域 */
.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.app-content-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-tertiary);
  background-color: var(--bg-primary);
}

.app-content-main {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background-color: var(--bg-primary);
}

/* 状态栏 */
.app-status-bar {
  height: var(--status-bar-height);
  padding: 0 var(--spacing-md);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.app-status-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* ================================
   导航组件
   ================================ */

.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-medium);
  margin: 0 var(--spacing-sm);
  transition: all var(--duration-fast) var(--easing-standard);
  position: relative;
}

.nav-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--color-primary);
}

.nav-link.active {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: var(--text-on-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nav-badge {
  margin-left: auto;
}

/* 折叠状态下的导航 */
.app-sidebar.collapsed .nav-text,
.app-sidebar.collapsed .nav-badge {
  display: none;
}

.app-sidebar.collapsed .nav-link {
  justify-content: center;
  padding: var(--spacing-sm);
}

/* ================================
   面板和分割器
   ================================ */

.panel {
  background-color: var(--bg-card);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-2);
}

.panel-header {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: var(--font-size-body-large);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.panel-body {
  padding: var(--spacing-md);
}

.panel-footer {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-tertiary);
}

/* 可调整大小的面板 */
.resizable-panel {
  position: relative;
  min-width: var(--panel-min-width);
}

.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 1;
}

.resize-handle-vertical {
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
}

.resize-handle-vertical.left {
  left: -2px;
}

.resize-handle-vertical.right {
  right: -2px;
}

.resize-handle-horizontal {
  left: 0;
  right: 0;
  height: 4px;
  cursor: row-resize;
}

.resize-handle-horizontal.top {
  top: -2px;
}

.resize-handle-horizontal.bottom {
  bottom: -2px;
}

.resize-handle:hover,
.resize-handle:active {
  background-color: var(--color-primary);
}

/* ================================
   网格布局系统
   ================================ */

.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }
.col-span-6 { grid-column: span 6; }
.col-span-12 { grid-column: span 12; }

/* Flexbox工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* ================================
   响应式设计 (1280×768优化)
   ================================ */

@media (max-width: 1280px) {
  .app-container {
    min-width: 1280px;
  }
  
  .app-sidebar {
    width: 240px;
  }
  
  .app-content-main {
    padding: var(--spacing-sm);
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-cols-6 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-height: 768px) {
  .app-content-main {
    padding: var(--spacing-sm);
  }
  
  .panel-body {
    padding: var(--spacing-sm);
  }
}
