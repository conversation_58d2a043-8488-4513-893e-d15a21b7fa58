<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirMonitor - 商用空调调试监控软件</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        /* 主页面临时样式 */
        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
        }

        .header {
            background: var(--color-primary);
            color: var(--text-on-primary);
            padding: var(--spacing-md) var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-4);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .logo {
            width: 40px;
            height: 40px;
            background: var(--text-on-primary);
            color: var(--color-primary);
            border-radius: var(--radius-medium);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-weight-bold);
            font-size: 18px;
        }

        .app-info h1 {
            margin: 0;
            font-size: var(--font-size-title);
            font-weight: var(--font-weight-semibold);
        }

        .app-info p {
            margin: 0;
            font-size: var(--font-size-body);
            opacity: 0.9;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .license-info {
            text-align: right;
        }

        .license-type {
            font-size: var(--font-size-body);
            font-weight: var(--font-weight-medium);
        }

        .license-status {
            font-size: var(--font-size-caption);
            opacity: 0.8;
        }

        .content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xxl);
        }

        .welcome-card {
            background: var(--bg-card);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-8);
            padding: var(--spacing-xxl);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .welcome-title {
            font-size: var(--font-size-display);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .welcome-subtitle {
            font-size: var(--font-size-body-large);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
            line-height: 1.6;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .status-item {
            background: var(--bg-secondary);
            border-radius: var(--radius-medium);
            padding: var(--spacing-md);
            text-align: center;
        }

        .status-value {
            font-size: var(--font-size-title);
            font-weight: var(--font-weight-semibold);
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
        }

        .status-label {
            font-size: var(--font-size-body);
            color: var(--text-secondary);
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            justify-content: center;
        }

        .development-notice {
            background: rgba(255, 140, 0, 0.1);
            border: 1px solid var(--color-warning);
            border-radius: var(--radius-medium);
            padding: var(--spacing-md);
            margin-top: var(--spacing-xl);
            text-align: left;
        }

        .development-notice h4 {
            color: var(--color-warning);
            margin: 0 0 var(--spacing-sm) 0;
            font-size: var(--font-size-body-large);
        }

        .development-notice p {
            margin: 0;
            font-size: var(--font-size-body);
            color: var(--text-primary);
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: var(--spacing-sm);
                text-align: center;
            }
            
            .user-section {
                justify-content: center;
            }
            
            .content {
                padding: var(--spacing-lg);
            }
            
            .welcome-card {
                padding: var(--spacing-lg);
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="logo-section">
                <div class="logo">AM</div>
                <div class="app-info">
                    <h1>AirMonitor</h1>
                    <p>商用空调调试监控软件</p>
                </div>
            </div>
            
            <div class="user-section">
                <div class="license-info" id="licenseInfo">
                    <div class="license-type">正在加载...</div>
                    <div class="license-status">检查激活状态</div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="content">
            <div class="welcome-card">
                <h2 class="welcome-title">欢迎使用 AirMonitor</h2>
                <p class="welcome-subtitle">
                    专业的商用空调调试监控软件，为您提供全面的设备监控、数据分析和调试功能。
                    基于Microsoft Fluent Design设计语言，为专业技术人员打造直观高效的操作体验。
                </p>

                <!-- 状态概览 -->
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="connectedDevices">0</div>
                        <div class="status-label">已连接设备</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="monitoringPoints">0</div>
                        <div class="status-label">监控点位</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="activeAlarms">0</div>
                        <div class="status-label">活动告警</div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-primary btn-large" onclick="navigateToConnection()">
                        连接设备
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="navigateToMonitor()">
                        设备监控
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="navigateToAnalysis()">
                        数据分析
                    </button>
                    <button class="btn btn-default btn-large" onclick="showSettings()">
                        系统设置
                    </button>
                </div>

                <!-- 开发状态提示 -->
                <div class="development-notice">
                    <h4>🚧 开发状态</h4>
                    <p>
                        当前为<strong>阶段1完成版本</strong>，已实现软件激活系统。
                        其他功能模块（设备连接、参数监控、数据分析等）正在开发中，
                        将按照开发路线图逐步完成。
                    </p>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="js/components/license-manager.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeMainPage();
        });

        function initializeMainPage() {
            // 检查激活状态
            checkActivationStatus();
            
            // 初始化状态数据
            initializeStatusData();
            
            // 监听License事件
            window.addEventListener('license:activated', handleLicenseActivated);
            window.addEventListener('license:cleared', handleLicenseCleared);
        }

        function checkActivationStatus() {
            if (!LicenseManager.isActivated()) {
                // 如果未激活，跳转到激活页面
                window.location.href = 'pages/activation.html';
                return;
            }

            // 显示License信息
            const status = LicenseManager.getStatus();
            updateLicenseDisplay(status);
        }

        function updateLicenseDisplay(status) {
            const licenseInfo = document.getElementById('licenseInfo');
            const licenseType = licenseInfo.querySelector('.license-type');
            const licenseStatus = licenseInfo.querySelector('.license-status');

            licenseType.textContent = LicenseManager.getLicenseTypeName(status.type);
            
            if (status.daysRemaining > 0) {
                licenseStatus.textContent = `剩余 ${status.daysRemaining} 天`;
            } else if (status.daysRemaining === -1) {
                licenseStatus.textContent = '永久授权';
            } else {
                licenseStatus.textContent = '已过期';
                licenseStatus.style.color = 'var(--color-error)';
            }
        }

        function initializeStatusData() {
            // 模拟状态数据
            document.getElementById('connectedDevices').textContent = '0';
            document.getElementById('monitoringPoints').textContent = '0';
            document.getElementById('activeAlarms').textContent = '0';
        }

        function handleLicenseActivated(event) {
            const license = event.detail;
            console.log('License激活成功:', license);
            updateLicenseDisplay(LicenseManager.getStatus());
        }

        function handleLicenseCleared() {
            console.log('License已清除');
            window.location.href = 'pages/activation.html';
        }

        // 导航函数
        function navigateToConnection() {
            alert('设备连接功能开发中...\n\n这将是阶段2的核心功能，包括：\n- 串口设备扫描\n- 设备连接管理\n- 通信协议处理');
        }

        function navigateToMonitor() {
            alert('设备监控功能开发中...\n\n这将是阶段3的核心功能，包括：\n- 实时参数监控\n- 数据曲线显示\n- 告警系统');
        }

        function navigateToAnalysis() {
            alert('数据分析功能开发中...\n\n这将是阶段4的核心功能，包括：\n- 历史数据分析\n- 趋势分析\n- 报表生成');
        }

        function showSettings() {
            const status = LicenseManager.getStatus();
            let message = '系统设置\n\n';
            message += `License类型: ${LicenseManager.getLicenseTypeName(status.type)}\n`;
            message += `激活状态: ${status.activated ? '已激活' : '未激活'}\n`;
            message += `最大设备数: ${status.maxDevices === -1 ? '无限制' : status.maxDevices}\n`;
            message += `公司名称: ${status.companyName || '未设置'}\n\n`;
            message += '权限列表:\n';
            status.permissions.forEach(permission => {
                message += `- ${LicenseManager.getPermissionName(permission)}\n`;
            });
            
            alert(message);
        }

        // 开发者工具函数
        function clearLicense() {
            if (confirm('确定要清除License信息吗？这将需要重新激活软件。')) {
                LicenseManager.clearLicense();
            }
        }

        // 在控制台中提供开发者工具
        console.log('AirMonitor 开发者工具:');
        console.log('- clearLicense(): 清除License信息');
        console.log('- LicenseManager: License管理器对象');
    </script>
</body>
</html>
