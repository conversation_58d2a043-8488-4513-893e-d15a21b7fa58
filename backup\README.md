# AirMonitor - 商用空调调试监控软件 PC端原型

## 项目概述

**AirMonitor** 是一款专业的商用空调调试监控软件，专为空调设备的调试、监控和维护而设计。本项目为PC端原型界面，采用Microsoft Fluent Design System设计语言，为专业技术人员提供高效、直观的操作体验。

## 项目定位

- **应用类型**: 商用空调调试监控软件
- **平台**: PC桌面应用程序
- **核心用途**: 监控机组运行参数、设备调试、参数设置等专业操作
- **设计语言**: Microsoft Fluent Design System
- **主色调**: #FF005FB8（深蓝色）

## 目标用户群体

### 主要用户角色
1. **调试工人** - 现场设备调试操作人员
   - 需要直观的设备状态显示
   - 简化的操作流程
   - 清晰的故障提示

2. **售后人员** - 设备维护和故障处理人员
   - 详细的设备参数监控
   - 历史数据分析功能
   - 快速故障诊断工具

3. **售后主管** - 售后团队管理和监督人员
   - 整体设备状态概览
   - 团队工作进度监控
   - 数据报表和分析

4. **研发人员** - 产品开发和技术支持人员
   - 深度参数调试功能
   - 数据导出和分析
   - 高级配置选项

## 核心功能模块

### 1. 连接管理模块
- **串口连接管理**: 设备连接状态监控和管理
- **设备识别**: 自动识别连接的空调设备
- **连接状态指示**: 实时显示连接状态

### 2. 数据监控模块
- **数据帧监听**: 实时监听设备数据传输
- **数据解析**: 自动解析设备数据协议
- **实时参数监测**: 温度、压力、电流等关键参数实时显示

### 3. 设备控制模块
- **内机控制操作**: 远程控制内机运行状态
- **负载强制控制**: 强制设备负载运行测试
- **参数设置**: 设备运行参数配置

### 4. 数据分析模块
- **实时数据曲线**: 关键参数的实时曲线图表
- **历史数据回放**: 历史运行数据回放功能
- **回放曲线分析**: 历史数据的图表分析

### 5. 调试辅助模块
- **调试步骤向导**: 标准化调试流程指导
- **EEPROM读写**: 设备存储器读写操作
- **故障诊断**: 自动故障检测和诊断

### 6. 权限管理模块
- **License管理**: 基于设备指纹的权限控制
- **用户角色**: 不同用户角色的功能权限管理
- **操作日志**: 用户操作记录和审计

## 设计规范

### 视觉设计规范
- **设计系统**: Microsoft Fluent Design System
- **主色调**: #FF005FB8（深蓝色）
- **辅助色**: #FF0078D4（浅蓝色）、#FF107C10（绿色）、#FFFF4B4B（红色）
- **中性色**: #FF323130（深灰）、#FF605E5C（中灰）、#FFF3F2F1（浅灰）

### 布局规范
- **最低分辨率**: 1280×768
- **网格系统**: 8px基础网格
- **间距系统**: 4px、8px、16px、24px、32px
- **圆角**: 2px（小元素）、4px（卡片）、8px（面板）

### 字体规范
- **主字体**: Microsoft YaHei UI
- **标题字体**: 18px/24px/32px
- **正文字体**: 14px/16px
- **辅助文字**: 12px

## 技术架构

### 前端技术栈
- **HTML5**: 语义化标签结构
- **CSS3**: Flexbox/Grid布局，CSS变量
- **JavaScript ES6+**: 模块化开发
- **Chart.js**: 数据图表展示
- **WebSerial API**: 串口通信（原型模拟）

### 组件架构
- **布局组件**: 头部导航、侧边栏、主内容区
- **功能组件**: 数据表格、图表、控制面板
- **交互组件**: 按钮、表单、弹窗、提示

## 页面结构规划

### 主要页面
1. **主控制台** (`index.html`) - 设备状态总览和快速操作
2. **设备监控** (`pages/monitor.html`) - 实时参数监控和数据展示
3. **数据分析** (`pages/analysis.html`) - 历史数据分析和图表展示
4. **设备控制** (`pages/control.html`) - 设备参数设置和控制操作
5. **调试向导** (`pages/debug.html`) - 标准化调试流程指导
6. **系统设置** (`pages/settings.html`) - 软件配置和用户管理

### 响应式设计
- **桌面端优先**: 针对1280×768及以上分辨率优化
- **布局适配**: 支持窗口缩放和多显示器
- **字体缩放**: 支持系统字体缩放设置

## 开发状态

### 当前阶段: 项目初始化完成 ✅
- [x] 项目结构创建
- [x] 需求分析完成
- [x] 设计规范制定
- [x] 技术架构规划

### 下一步计划
- [ ] UI/UX详细设计分析
- [ ] 设计系统和主题创建
- [ ] 核心组件开发
- [ ] 主要页面实现
- [ ] 交互功能开发
- [ ] 原型测试和优化

## 使用指南

### 快速开始
1. 在浏览器中打开 `index.html` 查看主控制台
2. 使用导航菜单切换不同功能模块
3. 查看各页面的功能演示和交互效果

### 开发指令
- `/设计` - 进行详细的UI/UX设计分析
- `/开发` - 开始原型开发实现
- `/组件 <名称>` - 开发特定UI组件
- `/页面 <名称>` - 开发特定功能页面
- `/主题` - 设置Fluent Design主题
- `/交互` - 添加交互功能
- `/预览` - 预览原型效果

## 项目特色

### 专业性
- 针对空调调试专业需求设计
- 符合工业软件操作习惯
- 专业术语和界面元素

### 易用性
- Microsoft Fluent Design语言
- 直观的操作流程
- 清晰的信息层次

### 功能性
- 完整的设备监控功能
- 强大的数据分析能力
- 灵活的权限管理系统

---

**项目创建时间**: 2025年6月18日  
**设计师**: AI UI/UX设计助手  
**版本**: v1.0.0-prototype
