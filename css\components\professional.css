/**
 * 专业工具软件组件样式
 * 专门为空调调试监控软件设计的专业组件
 */

/* ================================
   连接状态组件
   ================================ */

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-body);
}

.connection-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.connection-indicator.connected {
  background-color: var(--color-success);
  box-shadow: 0 0 0 2px rgba(16, 124, 16, 0.2);
  animation: pulse-success 2s infinite;
}

.connection-indicator.disconnected {
  background-color: var(--color-neutral-40);
}

.connection-indicator.connecting {
  background-color: var(--color-warning);
  animation: pulse-warning 1s infinite;
}

.connection-indicator.error {
  background-color: var(--color-error);
  animation: pulse-error 1s infinite;
}

@keyframes pulse-success {
  0%, 100% { box-shadow: 0 0 0 2px rgba(16, 124, 16, 0.2); }
  50% { box-shadow: 0 0 0 4px rgba(16, 124, 16, 0.4); }
}

@keyframes pulse-warning {
  0%, 100% { box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.2); }
  50% { box-shadow: 0 0 0 4px rgba(255, 140, 0, 0.4); }
}

@keyframes pulse-error {
  0%, 100% { box-shadow: 0 0 0 2px rgba(209, 52, 56, 0.2); }
  50% { box-shadow: 0 0 0 4px rgba(209, 52, 56, 0.4); }
}

.connection-text {
  font-weight: var(--font-weight-medium);
}

.connection-details {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  margin-left: auto;
}

/* ================================
   参数监控卡片
   ================================ */

.parameter-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-large);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-2);
  transition: all var(--duration-normal) var(--easing-standard);
  position: relative;
  overflow: hidden;
}

.parameter-card:hover {
  box-shadow: var(--shadow-4);
  border-color: var(--border-secondary);
}

.parameter-card.alert {
  border-color: var(--color-error);
  background-color: rgba(209, 52, 56, 0.05);
}

.parameter-card.warning {
  border-color: var(--color-warning);
  background-color: rgba(255, 140, 0, 0.05);
}

.parameter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.parameter-name {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.parameter-unit {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-small);
}

.parameter-value {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
  font-family: var(--font-family-mono);
}

.parameter-value.alert {
  color: var(--color-error);
}

.parameter-value.warning {
  color: var(--color-warning);
}

.parameter-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.parameter-trend-icon {
  width: 12px;
  height: 12px;
}

.parameter-trend.up {
  color: var(--color-success);
}

.parameter-trend.down {
  color: var(--color-error);
}

.parameter-trend.stable {
  color: var(--text-secondary);
}

/* ================================
   数据表格组件
   ================================ */

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-card);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-2);
}

.data-table th {
  background-color: var(--bg-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-tertiary);
}

.data-table td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-tertiary);
  font-size: var(--font-size-body);
  color: var(--text-primary);
}

.data-table tr:hover {
  background-color: var(--bg-secondary);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table .numeric {
  text-align: right;
  font-family: var(--font-family-mono);
}

.data-table .status-cell {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* ================================
   控制面板组件
   ================================ */

.control-panel {
  background-color: var(--bg-card);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-large);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-2);
}

.control-group {
  margin-bottom: var(--spacing-md);
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.control-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.control-row:last-child {
  margin-bottom: 0;
}

/* 数值调节器 */
.numeric-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-medium);
  padding: var(--spacing-xs);
}

.numeric-input {
  border: none;
  background: transparent;
  text-align: center;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-body);
  color: var(--text-primary);
  width: 80px;
  padding: var(--spacing-xs);
}

.numeric-input:focus {
  outline: none;
  background-color: var(--bg-primary);
  border-radius: var(--radius-small);
}

.numeric-button {
  width: 24px;
  height: 24px;
  border: none;
  background-color: var(--color-primary);
  color: var(--text-on-primary);
  border-radius: var(--radius-small);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: background-color var(--duration-fast) var(--easing-standard);
}

.numeric-button:hover {
  background-color: var(--color-primary-hover);
}

.numeric-button:active {
  background-color: var(--color-primary-pressed);
}

/* ================================
   告警和通知组件
   ================================ */

.alert {
  padding: var(--spacing-md);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  border-left: 4px solid;
}

.alert-success {
  background-color: rgba(16, 124, 16, 0.1);
  border-left-color: var(--color-success);
  color: var(--color-success-dark);
}

.alert-warning {
  background-color: rgba(255, 140, 0, 0.1);
  border-left-color: var(--color-warning);
  color: var(--color-warning-dark);
}

.alert-error {
  background-color: rgba(209, 52, 56, 0.1);
  border-left-color: var(--color-error);
  color: var(--color-error-dark);
}

.alert-info {
  background-color: rgba(0, 120, 212, 0.1);
  border-left-color: var(--color-info);
  color: var(--color-info-dark);
}

.alert-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
}

.alert-message {
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
}

.alert-actions {
  margin-top: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-sm);
}

/* ================================
   工具栏组件
   ================================ */

.toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-medium);
  margin-bottom: var(--spacing-md);
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding-right: var(--spacing-md);
  border-right: 1px solid var(--border-tertiary);
}

.toolbar-group:last-child {
  border-right: none;
  padding-right: 0;
  margin-left: auto;
}

.toolbar-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid transparent;
  background-color: transparent;
  color: var(--text-primary);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-standard);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-body);
}

.toolbar-button:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.toolbar-button.active {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
  border-color: var(--color-primary);
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background-color: var(--border-tertiary);
  margin: 0 var(--spacing-xs);
}
