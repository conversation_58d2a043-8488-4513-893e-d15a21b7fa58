# AirMonitor - 商用空调调试监控软件 PC端原型

## 项目概述

**AirMonitor** 是一款专业的商用空调调试监控软件，专为空调设备的调试、监控和维护而设计。本项目为PC端原型界面，采用Microsoft Fluent Design System设计语言，为专业技术人员提供高效、直观的操作体验。

## 项目定位

- **应用类型**: 商用空调调试监控软件
- **平台**: PC桌面应用程序
- **核心用途**: 监控机组运行参数、设备调试、参数设置等专业操作
- **设计语言**: Microsoft Fluent Design System
- **主色调**: #FF005FB8（深蓝色）

## 目标用户群体

### 主要用户角色
1. **调试工人** - 现场设备调试操作人员
   - 需要直观的设备状态显示
   - 简化的操作流程
   - 清晰的故障提示

2. **售后人员** - 设备维护和故障处理人员
   - 详细的设备参数监控
   - 历史数据分析功能
   - 快速故障诊断工具

3. **售后主管** - 售后团队管理和监督人员
   - 整体设备状态概览
   - 团队工作进度监控
   - 数据报表和分析

4. **研发人员** - 产品开发和技术支持人员
   - 深度参数调试功能
   - 数据导出和分析
   - 高级配置选项

## 核心功能模块

### 1. 连接管理模块
- **串口连接管理**: 设备连接状态监控和管理
- **设备识别**: 自动识别连接的空调设备
- **连接状态指示**: 实时显示连接状态

### 2. 数据监控模块
- **数据帧监听**: 实时监听设备数据传输
- **数据解析**: 自动解析设备数据协议
- **实时参数监测**: 温度、压力、电流等关键参数实时显示

### 3. 设备控制模块
- **内机控制操作**: 远程控制内机运行状态
- **负载强制控制**: 强制设备负载运行测试
- **参数设置**: 设备运行参数配置

### 4. 数据分析模块
- **实时数据曲线**: 关键参数的实时曲线图表
- **历史数据回放**: 历史运行数据回放功能
- **回放曲线分析**: 历史数据的图表分析

### 5. 调试辅助模块
- **调试步骤向导**: 标准化调试流程指导
- **EEPROM读写**: 设备存储器读写操作
- **故障诊断**: 自动故障检测和诊断

### 6. 权限管理模块
- **License管理**: 基于设备指纹的权限控制
- **用户角色**: 不同用户角色的功能权限管理
- **操作日志**: 用户操作记录和审计

## 设计规范

### 视觉设计规范
- **设计系统**: Microsoft Fluent Design System
- **主色调**: #FF005FB8（深蓝色）
- **辅助色**: #FF0078D4（浅蓝色）、#FF107C10（绿色）、#FFFF4B4B（红色）
- **中性色**: #FF323130（深灰）、#FF605E5C（中灰）、#FFF3F2F1（浅灰）

### 布局规范
- **最低分辨率**: 1280×768
- **网格系统**: 8px基础网格
- **间距系统**: 4px、8px、16px、24px、32px
- **圆角**: 2px（小元素）、4px（卡片）、8px（面板）

### 字体规范
- **主字体**: Microsoft YaHei UI
- **标题字体**: 18px/24px/32px
- **正文字体**: 14px/16px
- **辅助文字**: 12px

## 技术架构

### 前端技术栈
- **HTML5**: 语义化标签结构
- **CSS3**: Flexbox/Grid布局，CSS变量
- **JavaScript ES6+**: 模块化开发
- **Chart.js**: 数据图表展示
- **WebSerial API**: 串口通信（原型模拟）

### 组件架构
- **布局组件**: 头部导航、侧边栏、主内容区
- **功能组件**: 数据表格、图表、控制面板
- **交互组件**: 按钮、表单、弹窗、提示

## 页面结构规划

### 主要页面
1. **主控制台** (`index.html`) - 设备状态总览和快速操作
2. **设备监控** (`pages/monitor.html`) - 实时参数监控和数据展示
3. **数据分析** (`pages/analysis.html`) - 历史数据分析和图表展示
4. **设备控制** (`pages/control.html`) - 设备参数设置和控制操作
5. **调试向导** (`pages/debug.html`) - 标准化调试流程指导
6. **系统设置** (`pages/settings.html`) - 软件配置和用户管理

### 响应式设计
- **桌面端优先**: 针对1280×768及以上分辨率优化
- **布局适配**: 支持窗口缩放和多显示器
- **字体缩放**: 支持系统字体缩放设置

## 开发路线图

### 开发节奏规划原则

基于商用空调调试监控软件的实际使用流程，我们采用**用户旅程驱动**的开发方式，确保每个开发阶段都对应真实的用户操作场景，从软件启动到完成调试的完整工作流程。

### 用户操作流程分析

```mermaid
graph TD
    A[启动软件] --> B[License验证]
    B --> C{激活状态}
    C -->|未激活| D[设备指纹识别]
    D --> E[输入激活码]
    E --> F[权限验证]
    F --> C
    C -->|已激活| G[进入主界面]
    G --> H[扫描串口设备]
    H --> I[选择连接设备]
    I --> J[建立通信连接]
    J --> K{连接状态}
    K -->|连接失败| H
    K -->|连接成功| L[开始参数监控]
    L --> M[实时数据显示]
    M --> N[数据曲线分析]
    N --> O[设备控制操作]
    O --> P[调试向导执行]
    P --> Q[历史数据分析]
    Q --> R[完成调试任务]
```

## 详细开发计划

### 🚀 阶段1：软件激活系统开发 (预计3-4天)

**核心目标**: 实现基于设备指纹的License权限管理系统

#### 1.1 页面设计与开发 (1.5天)
- **激活主界面** (`pages/activation.html`)
  - 软件Logo和版本信息展示
  - 设备指纹自动识别显示
  - 激活码输入区域
  - 激活状态指示器
  - 离线激活选项

#### 1.2 核心功能实现 (1.5天)
- **设备指纹生成算法** (`js/utils/fingerprint.js`)
  - CPU信息、MAC地址、硬盘序列号等硬件信息收集
  - 指纹哈希算法实现
  - 指纹唯一性验证

- **License验证系统** (`js/components/license-manager.js`)
  - 激活码格式验证
  - 权限级别解析
  - 到期时间管理
  - 功能模块权限控制

#### 1.3 用户体验优化 (1天)
- **状态反馈系统**
  - 激活进度指示
  - 错误信息友好提示
  - 成功激活动画效果
- **权限管理界面**
  - 当前权限状态显示
  - 功能模块可用性指示
  - 权限到期提醒

**里程碑**: 用户可以完成软件激活，进入主功能界面

---

### 🔌 阶段2：设备连接管理开发 (预计4-5天)

**核心目标**: 实现串口设备的自动发现、连接管理和通信建立

#### 2.1 连接管理界面开发 (2天)
- **设备连接主页面** (`pages/connection.html`)
  - 可用串口列表显示
  - 设备扫描和刷新功能
  - 连接参数配置面板
  - 连接状态实时监控

- **连接向导组件** (`js/components/connection-wizard.js`)
  - 自动扫描串口设备
  - 设备类型自动识别
  - 连接参数智能推荐
  - 连接测试和验证

#### 2.2 串口通信核心功能 (2天)
- **串口管理器** (`js/utils/serial-manager.js`)
  - 串口枚举和监控
  - 连接建立和断开
  - 数据收发管理
  - 连接状态监控

- **设备识别系统** (`js/utils/device-identifier.js`)
  - 设备握手协议实现
  - 设备型号自动识别
  - 固件版本检测
  - 设备能力查询

#### 2.3 连接状态管理 (1天)
- **状态监控面板**
  - 连接状态实时指示
  - 通信质量监控
  - 错误统计和诊断
  - 自动重连机制

**里程碑**: 用户可以成功连接空调设备，建立稳定通信

---

### 📊 阶段3：参数监控系统开发 (预计5-6天)

**核心目标**: 实现实时参数监测、数据曲线显示和告警系统

#### 3.1 监控主界面开发 (2天)
- **参数监控仪表板** (`pages/monitor.html`)
  - 关键参数卡片式展示
  - 实时数值更新显示
  - 参数状态颜色指示
  - 告警信息集中显示

- **参数分组管理**
  - 温度参数组（进出水温、环境温度等）
  - 压力参数组（高低压、油压等）
  - 电气参数组（电流、电压、功率等）
  - 运行参数组（频率、转速、开度等）

#### 3.2 实时数据曲线系统 (2天)
- **图表组件开发** (`js/components/realtime-chart.js`)
  - 多参数实时曲线显示
  - 时间轴自动滚动
  - 曲线颜色和样式配置
  - 数据点悬停详情显示

- **数据缓存管理** (`js/utils/data-buffer.js`)
  - 实时数据环形缓存
  - 数据采样率控制
  - 内存使用优化
  - 数据导出功能

#### 3.3 告警和诊断系统 (1.5天)
- **告警规则引擎** (`js/utils/alarm-engine.js`)
  - 参数阈值监控
  - 多级告警分类
  - 告警历史记录
  - 告警确认和处理

- **智能诊断系统**
  - 异常模式识别
  - 故障原因分析
  - 维修建议提供
  - 诊断报告生成

#### 3.4 数据记录和存储 (0.5天)
- **本地数据存储**
  - 实时数据本地缓存
  - 历史数据文件管理
  - 数据压缩和归档
  - 数据导出格式支持

**里程碑**: 用户可以实时监控设备参数，查看数据趋势，接收告警信息

---

### ⚙️ 阶段4：核心操作功能开发 (预计6-7天)

**核心目标**: 实现设备控制、调试向导、历史数据分析等高级功能

#### 4.1 设备控制系统 (2.5天)
- **控制操作界面** (`pages/control.html`)
  - 设备启停控制
  - 运行模式切换
  - 参数设定和调节
  - 强制负载控制

- **参数设置面板** (`js/components/parameter-panel.js`)
  - 分类参数设置界面
  - 参数有效性验证
  - 批量参数导入导出
  - 参数变更历史记录

#### 4.2 调试向导系统 (2天)
- **调试向导界面** (`pages/debug-wizard.html`)
  - 标准调试流程指导
  - 步骤进度跟踪
  - 操作提示和帮助
  - 调试结果验证

- **EEPROM操作工具** (`js/components/eeprom-tool.js`)
  - 存储器读写操作
  - 数据格式化显示
  - 备份和恢复功能
  - 数据完整性校验

#### 4.3 历史数据分析 (1.5天)
- **数据分析界面** (`pages/analysis.html`)
  - 历史数据查询和筛选
  - 多参数对比分析
  - 趋势分析和预测
  - 分析报告生成

- **数据回放系统** (`js/components/data-playback.js`)
  - 历史数据时间轴回放
  - 回放速度控制
  - 关键时刻标记
  - 回放数据导出

**里程碑**: 用户可以完成完整的设备调试流程，进行深度数据分析

## 技术实现依赖关系

### 开发顺序依赖图

```mermaid
graph TD
    A[基础设计系统] --> B[激活系统UI]
    A --> C[连接管理UI]
    A --> D[监控系统UI]
    A --> E[控制系统UI]

    B --> F[License验证逻辑]
    F --> G[权限管理系统]

    C --> H[串口通信核心]
    H --> I[设备识别系统]
    I --> J[连接状态管理]

    G --> J
    J --> K[实时数据采集]
    K --> L[数据缓存系统]
    L --> M[图表显示组件]
    M --> N[告警系统]

    N --> O[设备控制逻辑]
    O --> P[参数设置系统]
    P --> Q[调试向导系统]

    L --> R[历史数据管理]
    R --> S[数据分析系统]
    S --> T[报告生成系统]
```

### 关键技术组件

#### 核心工具库 (优先开发)
1. **设计系统** (`css/themes/fluent-design.css`) - 所有界面的基础
2. **工具函数库** (`js/utils/common.js`) - 通用功能支持
3. **状态管理** (`js/utils/state-manager.js`) - 全局状态控制
4. **事件系统** (`js/utils/event-bus.js`) - 组件间通信

#### 数据处理层 (第二优先级)
1. **数据模型** (`js/models/`) - 数据结构定义
2. **通信协议** (`js/protocols/`) - 设备通信协议
3. **数据验证** (`js/validators/`) - 数据有效性检查

#### 业务逻辑层 (第三优先级)
1. **业务服务** (`js/services/`) - 核心业务逻辑
2. **工作流引擎** (`js/workflows/`) - 调试流程控制

## 开发状态

### 当前阶段: 开发路线图制定完成 ✅
- [x] 项目结构创建
- [x] 需求分析完成
- [x] 设计规范制定
- [x] 技术架构规划
- [x] 详细开发路线图制定

### 即将开始: 阶段1 - 软件激活系统开发 🚀
- [ ] Fluent Design设计系统创建
- [ ] 激活界面UI设计和开发
- [ ] 设备指纹识别功能实现
- [ ] License验证系统开发
- [ ] 权限管理功能实现

## 项目时间规划

### 总体开发周期: 18-22天

| 阶段 | 功能模块 | 预计时间 | 关键里程碑 | 交付物 |
|------|----------|----------|------------|---------|
| **阶段1** | 软件激活系统 | 3-4天 | 用户可完成软件激活 | 激活页面、License管理系统 |
| **阶段2** | 设备连接管理 | 4-5天 | 成功连接空调设备 | 连接页面、串口通信系统 |
| **阶段3** | 参数监控系统 | 5-6天 | 实时监控设备参数 | 监控页面、数据图表系统 |
| **阶段4** | 核心操作功能 | 6-7天 | 完整调试流程实现 | 控制页面、调试向导系统 |

### 关键里程碑节点

#### 🎯 里程碑1: 软件基础框架完成 (第4天)
**验收标准**:
- [ ] 用户可以启动软件并完成激活
- [ ] 基础UI框架和设计系统建立
- [ ] 权限管理系统正常工作
- [ ] 可以进入主功能界面

#### 🎯 里程碑2: 设备通信建立 (第9天)
**验收标准**:
- [ ] 可以扫描和识别串口设备
- [ ] 成功建立与空调设备的通信连接
- [ ] 连接状态实时监控正常
- [ ] 基础数据收发功能正常

#### 🎯 里程碑3: 核心监控功能完成 (第15天)
**验收标准**:
- [ ] 实时参数监控界面完整展示
- [ ] 数据曲线实时更新显示
- [ ] 告警系统正常工作
- [ ] 数据记录和存储功能正常

#### 🎯 里程碑4: 完整原型交付 (第22天)
**验收标准**:
- [ ] 所有核心功能模块完整实现
- [ ] 用户可以完成完整的调试流程
- [ ] 历史数据分析功能正常
- [ ] 原型文档和使用指南完整

### 风险控制和应急预案

#### 技术风险
1. **串口通信兼容性问题**
   - 风险等级: 中等
   - 应对策略: 提前准备多种通信协议适配方案
   - 预留时间: +1天

2. **实时数据处理性能问题**
   - 风险等级: 中等
   - 应对策略: 采用数据缓存和异步处理机制
   - 预留时间: +1天

3. **复杂业务逻辑实现难度**
   - 风险等级: 低等
   - 应对策略: 分步骤实现，优先核心功能
   - 预留时间: +1天

#### 进度风险
1. **功能需求变更**
   - 应对策略: 采用模块化设计，支持快速调整
   - 沟通机制: 每个里程碑节点进行需求确认

2. **技术难点攻关时间超预期**
   - 应对策略: 关键技术点提前验证和原型测试
   - 缓冲时间: 总体预留3-4天缓冲时间

## 使用指南

### 快速开始
1. 在浏览器中打开 `index.html` 查看主控制台
2. 使用导航菜单切换不同功能模块
3. 查看各页面的功能演示和交互效果

### 开发指令
- `/设计` - 进行详细的UI/UX设计分析
- `/开发` - 开始原型开发实现
- `/组件 <名称>` - 开发特定UI组件
- `/页面 <名称>` - 开发特定功能页面
- `/主题` - 设置Fluent Design主题
- `/交互` - 添加交互功能
- `/预览` - 预览原型效果

## 项目特色

### 专业性
- 针对空调调试专业需求设计
- 符合工业软件操作习惯
- 专业术语和界面元素

### 易用性
- Microsoft Fluent Design语言
- 直观的操作流程
- 清晰的信息层次

### 功能性
- 完整的设备监控功能
- 强大的数据分析能力
- 灵活的权限管理系统

---

**项目创建时间**: 2025年6月18日  
**设计师**: AI UI/UX设计助手  
**版本**: v1.0.0-prototype
