/**
 * License管理器
 * License Manager for AirMonitor
 * 
 * 功能：
 * - 激活码验证和激活
 * - 权限管理和控制
 * - License状态监控
 * - 到期时间管理
 */

class LicenseManager {
    static STORAGE_KEY = 'airmonitor_license';
    static ACTIVATION_ENDPOINT = '/api/activate'; // 实际项目中的激活接口
    
    // License类型定义
    static LICENSE_TYPES = {
        TRIAL: 'trial',           // 试用版
        STANDARD: 'standard',     // 标准版
        PROFESSIONAL: 'professional', // 专业版
        ENTERPRISE: 'enterprise'  // 企业版
    };

    // 权限定义
    static PERMISSIONS = {
        BASIC_MONITORING: 'basic_monitoring',     // 基础监控
        ADVANCED_ANALYSIS: 'advanced_analysis',  // 高级分析
        DEVICE_CONTROL: 'device_control',        // 设备控制
        DATA_EXPORT: 'data_export',              // 数据导出
        MULTI_DEVICE: 'multi_device',            // 多设备支持
        CUSTOM_REPORTS: 'custom_reports',        // 自定义报表
        API_ACCESS: 'api_access',                // API访问
        ADMIN_PANEL: 'admin_panel'               // 管理面板
    };

    /**
     * 验证激活码格式
     * @param {string} code 激活码
     * @returns {boolean} 格式是否正确
     */
    static validateCodeFormat(code) {
        if (!code) return false;
        
        // 移除所有非字母数字字符
        const cleanCode = code.replace(/[^A-Z0-9]/g, '');
        
        // 检查长度（应该是25个字符，5组每组5个）
        if (cleanCode.length !== 25) return false;
        
        // 检查格式（XXXXX-XXXXX-XXXXX-XXXXX-XXXXX）
        const pattern = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
        const formattedCode = this.formatActivationCode(code);
        
        return pattern.test(formattedCode);
    }

    /**
     * 格式化激活码
     * @param {string} code 原始激活码
     * @returns {string} 格式化后的激活码
     */
    static formatActivationCode(code) {
        if (!code) return '';
        
        const cleanCode = code.replace(/[^A-Z0-9]/g, '').toUpperCase();
        let formatted = '';
        
        for (let i = 0; i < cleanCode.length && i < 25; i++) {
            if (i > 0 && i % 5 === 0) {
                formatted += '-';
            }
            formatted += cleanCode[i];
        }
        
        return formatted;
    }

    /**
     * 激活软件
     * @param {string} activationCode 激活码
     * @param {string} deviceFingerprint 设备指纹
     * @returns {Promise<Object>} 激活结果
     */
    static async activate(activationCode, deviceFingerprint) {
        try {
            // 验证输入参数
            if (!activationCode || !deviceFingerprint) {
                throw new Error('激活码和设备指纹不能为空');
            }

            // 验证激活码格式
            if (!this.validateCodeFormat(activationCode)) {
                throw new Error('激活码格式不正确');
            }

            const formattedCode = this.formatActivationCode(activationCode);
            
            // 模拟网络请求延迟
            await this.delay(2000);
            
            // 在实际项目中，这里会调用服务器API
            const result = await this.simulateActivation(formattedCode, deviceFingerprint);
            
            if (result.success) {
                // 保存License信息到本地存储
                this.saveLicense(result.license);
                
                // 触发激活成功事件
                this.dispatchEvent('license:activated', result.license);
            }
            
            return result;
        } catch (error) {
            console.error('激活失败:', error);
            return {
                success: false,
                message: error.message || '激活过程中发生未知错误'
            };
        }
    }

    /**
     * 模拟激活过程（实际项目中替换为真实API调用）
     */
    static async simulateActivation(activationCode, deviceFingerprint) {
        // 模拟一些预设的激活码用于演示
        const validCodes = {
            'TRIAL-00000-00000-00000-00001': {
                type: this.LICENSE_TYPES.TRIAL,
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
                permissions: [
                    this.PERMISSIONS.BASIC_MONITORING
                ]
            },
            'STAND-12345-67890-ABCDE-FGHIJ': {
                type: this.LICENSE_TYPES.STANDARD,
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
                permissions: [
                    this.PERMISSIONS.BASIC_MONITORING,
                    this.PERMISSIONS.DEVICE_CONTROL,
                    this.PERMISSIONS.DATA_EXPORT
                ]
            },
            'PROFE-SSION-AL123-45678-90ABC': {
                type: this.LICENSE_TYPES.PROFESSIONAL,
                expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
                permissions: [
                    this.PERMISSIONS.BASIC_MONITORING,
                    this.PERMISSIONS.ADVANCED_ANALYSIS,
                    this.PERMISSIONS.DEVICE_CONTROL,
                    this.PERMISSIONS.DATA_EXPORT,
                    this.PERMISSIONS.MULTI_DEVICE,
                    this.PERMISSIONS.CUSTOM_REPORTS
                ]
            },
            'ENTER-PRISE-12345-67890-ABCDE': {
                type: this.LICENSE_TYPES.ENTERPRISE,
                expiresAt: null, // 永久License
                permissions: Object.values(this.PERMISSIONS)
            }
        };

        if (validCodes[activationCode]) {
            const licenseInfo = validCodes[activationCode];
            
            return {
                success: true,
                license: {
                    activationCode: activationCode,
                    deviceFingerprint: deviceFingerprint,
                    type: licenseInfo.type,
                    activatedAt: new Date(),
                    expiresAt: licenseInfo.expiresAt,
                    permissions: licenseInfo.permissions,
                    userId: 'demo-user-' + Date.now(),
                    companyName: '演示公司',
                    maxDevices: this.getMaxDevices(licenseInfo.type)
                }
            };
        } else {
            return {
                success: false,
                message: '激活码无效或已被使用'
            };
        }
    }

    /**
     * 获取不同License类型的最大设备数
     */
    static getMaxDevices(licenseType) {
        const deviceLimits = {
            [this.LICENSE_TYPES.TRIAL]: 1,
            [this.LICENSE_TYPES.STANDARD]: 5,
            [this.LICENSE_TYPES.PROFESSIONAL]: 20,
            [this.LICENSE_TYPES.ENTERPRISE]: -1 // 无限制
        };
        
        return deviceLimits[licenseType] || 1;
    }

    /**
     * 保存License信息到本地存储
     */
    static saveLicense(license) {
        try {
            const licenseData = {
                ...license,
                activatedAt: license.activatedAt.toISOString(),
                expiresAt: license.expiresAt ? license.expiresAt.toISOString() : null
            };
            
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(licenseData));
        } catch (error) {
            console.error('保存License信息失败:', error);
        }
    }

    /**
     * 从本地存储加载License信息
     */
    static loadLicense() {
        try {
            const licenseData = localStorage.getItem(this.STORAGE_KEY);
            if (!licenseData) return null;
            
            const license = JSON.parse(licenseData);
            
            // 转换日期字符串为Date对象
            license.activatedAt = new Date(license.activatedAt);
            license.expiresAt = license.expiresAt ? new Date(license.expiresAt) : null;
            
            return license;
        } catch (error) {
            console.error('加载License信息失败:', error);
            return null;
        }
    }

    /**
     * 检查是否已激活
     */
    static isActivated() {
        const license = this.loadLicense();
        if (!license) return false;
        
        // 检查是否过期
        if (license.expiresAt && new Date() > license.expiresAt) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否有特定权限
     */
    static hasPermission(permission) {
        const license = this.loadLicense();
        if (!license) return false;
        
        return license.permissions.includes(permission);
    }

    /**
     * 获取License状态信息
     */
    static getStatus() {
        const license = this.loadLicense();
        if (!license) {
            return {
                activated: false,
                type: null,
                expiresAt: null,
                daysRemaining: 0,
                permissions: []
            };
        }
        
        const now = new Date();
        const daysRemaining = license.expiresAt ? 
            Math.ceil((license.expiresAt - now) / (1000 * 60 * 60 * 24)) : -1;
        
        return {
            activated: this.isActivated(),
            type: license.type,
            expiresAt: license.expiresAt,
            daysRemaining: daysRemaining,
            permissions: license.permissions,
            maxDevices: license.maxDevices,
            companyName: license.companyName
        };
    }

    /**
     * 清除License信息（用于重置或注销）
     */
    static clearLicense() {
        try {
            localStorage.removeItem(this.STORAGE_KEY);
            this.dispatchEvent('license:cleared');
        } catch (error) {
            console.error('清除License信息失败:', error);
        }
    }

    /**
     * 工具方法：延迟执行
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 触发自定义事件
     */
    static dispatchEvent(eventName, data = null) {
        if (typeof window !== 'undefined') {
            const event = new CustomEvent(eventName, { detail: data });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取License类型的显示名称
     */
    static getLicenseTypeName(type) {
        const names = {
            [this.LICENSE_TYPES.TRIAL]: '试用版',
            [this.LICENSE_TYPES.STANDARD]: '标准版',
            [this.LICENSE_TYPES.PROFESSIONAL]: '专业版',
            [this.LICENSE_TYPES.ENTERPRISE]: '企业版'
        };
        
        return names[type] || '未知版本';
    }

    /**
     * 获取权限的显示名称
     */
    static getPermissionName(permission) {
        const names = {
            [this.PERMISSIONS.BASIC_MONITORING]: '基础监控',
            [this.PERMISSIONS.ADVANCED_ANALYSIS]: '高级分析',
            [this.PERMISSIONS.DEVICE_CONTROL]: '设备控制',
            [this.PERMISSIONS.DATA_EXPORT]: '数据导出',
            [this.PERMISSIONS.MULTI_DEVICE]: '多设备支持',
            [this.PERMISSIONS.CUSTOM_REPORTS]: '自定义报表',
            [this.PERMISSIONS.API_ACCESS]: 'API访问',
            [this.PERMISSIONS.ADMIN_PANEL]: '管理面板'
        };
        
        return names[permission] || permission;
    }
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.LicenseManager = LicenseManager;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LicenseManager;
}
