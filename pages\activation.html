<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirMonitor - 软件激活</title>
    <link rel="stylesheet" href="../css/main.css">
    <style>
        /* 激活页面专用样式 - 横版布局 */
        .activation-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
            padding: var(--spacing-lg);
        }

        .activation-card {
            background: var(--bg-card);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-64);
            width: 100%;
            max-width: 1200px;
            min-height: 500px;
            height: 500px;
            display: flex;
            overflow: hidden;
        }

        /* 左侧品牌展示区域 */
        .brand-section {
            flex: 1.2;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
            color: var(--text-on-primary);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .brand-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .app-logo {
            width: 80px;
            height: 80px;
            margin-bottom: var(--spacing-md);
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-large);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-on-primary);
            font-size: 32px;
            font-weight: var(--font-weight-bold);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .app-title {
            font-size: var(--font-size-display);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-xs);
            position: relative;
            z-index: 1;
        }

        .app-subtitle {
            font-size: var(--font-size-body);
            opacity: 0.9;
            margin-bottom: var(--spacing-md);
            position: relative;
            z-index: 1;
        }

        .version-info {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-medium);
            font-size: var(--font-size-caption);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .brand-features {
            margin-top: var(--spacing-md);
            position: relative;
            z-index: 1;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs);
            font-size: var(--font-size-caption);
            opacity: 0.9;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            flex-shrink: 0;
        }

        /* 右侧激活表单区域 */
        .activation-section {
            flex: 1.8;
            padding: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .activation-header {
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        .activation-title {
            font-size: var(--font-size-title);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .activation-description {
            font-size: var(--font-size-body);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-md);
        }

        .fingerprint-section {
            background: var(--bg-secondary);
            border-radius: var(--radius-medium);
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .fingerprint-label {
            font-size: var(--font-size-caption);
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
            display: block;
        }

        .fingerprint-value {
            font-family: var(--font-family-mono);
            font-size: var(--font-size-body);
            color: var(--text-primary);
            word-break: break-all;
            background: var(--bg-primary);
            padding: var(--spacing-sm);
            border-radius: var(--radius-small);
            border: 1px solid var(--border-tertiary);
        }

        .activation-form {
            margin-bottom: var(--spacing-md);
        }

        .activation-input {
            width: 100%;
            padding: var(--spacing-sm);
            font-size: var(--font-size-body);
            text-align: center;
            letter-spacing: 1px;
            font-family: var(--font-family-mono);
            margin-bottom: var(--spacing-sm);
        }

        .activation-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-xs);
            border-radius: var(--radius-medium);
            font-size: var(--font-size-body);
            margin-bottom: var(--spacing-sm);
        }

        .activation-status.pending {
            background: var(--bg-secondary);
            color: var(--text-secondary);
        }

        .activation-status.success {
            background: rgba(16, 124, 16, 0.1);
            color: var(--color-success);
        }

        .activation-status.error {
            background: rgba(209, 52, 56, 0.1);
            color: var(--color-error);
        }

        .activation-status.loading {
            background: rgba(0, 95, 184, 0.1);
            color: var(--color-primary);
        }

        .activation-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .btn-activate {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--font-size-body);
            font-weight: var(--font-weight-semibold);
        }

        .offline-activation {
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-tertiary);
        }

        .offline-link {
            color: var(--color-primary);
            text-decoration: none;
            font-size: var(--font-size-body);
            transition: color var(--duration-fast) var(--easing-standard);
        }

        .offline-link:hover {
            color: var(--color-primary-hover);
            text-decoration: underline;
        }

        .help-text {
            font-size: var(--font-size-caption);
            color: var(--text-tertiary);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .activation-card {
                max-width: 1000px;
            }
        }

        @media (max-width: 1024px) {
            .activation-card {
                flex-direction: column;
                max-width: 700px;
                height: auto;
                min-height: 600px;
            }

            .brand-section {
                flex: none;
                min-height: 250px;
                padding: var(--spacing-lg);
            }

            .activation-section {
                flex: none;
                padding: var(--spacing-lg);
            }
        }

        @media (max-width: 768px) {
            .activation-container {
                padding: var(--spacing-md);
            }

            .brand-section {
                min-height: 200px;
                padding: var(--spacing-md);
            }

            .activation-section {
                padding: var(--spacing-md);
            }

            .activation-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="activation-container">
        <div class="activation-card">
            <!-- 左侧品牌展示区域 -->
            <div class="brand-section">
                <div class="app-logo">
                    AM
                </div>

                <h1 class="app-title">AirMonitor</h1>
                <p class="app-subtitle">商用空调调试监控软件</p>

                <div class="version-info">
                    <span>版本 v1.0.0</span>
                    <span>•</span>
                    <span>专业版</span>
                </div>

                <div class="brand-features">
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>专业级设备监控</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>实时数据分析</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>智能调试向导</span>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <span>Microsoft Fluent Design</span>
                    </div>
                </div>
            </div>

            <!-- 右侧激活表单区域 -->
            <div class="activation-section">
                <div class="activation-header">
                    <h2 class="activation-title">软件激活</h2>
                    <p class="activation-description">请输入您的激活码来激活AirMonitor软件</p>
                </div>

                <!-- 设备指纹信息 -->
                <div class="fingerprint-section">
                    <label class="fingerprint-label">设备指纹 (Device Fingerprint)</label>
                    <div class="fingerprint-value" id="deviceFingerprint">
                        正在生成设备指纹...
                    </div>
                </div>

                <!-- 激活表单 -->
                <form class="activation-form" id="activationForm">
                    <div class="form-group">
                        <label class="form-label" for="activationCode">激活码 (Activation Code)</label>
                        <input
                            type="text"
                            id="activationCode"
                            class="input activation-input"
                            placeholder="请输入激活码"
                            maxlength="29"
                            autocomplete="off"
                        >
                        <div class="form-help">
                            激活码格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
                        </div>
                    </div>

                    <!-- 激活状态指示器 -->
                    <div class="activation-status pending" id="activationStatus">
                        <div class="status-dot offline"></div>
                        <span>等待激活</span>
                    </div>

                    <!-- 激活按钮 -->
                    <div class="activation-buttons">
                        <button type="submit" class="btn btn-primary btn-activate" id="activateBtn">
                            激活软件
                        </button>
                        <button type="button" class="btn btn-secondary" id="validateBtn">
                            验证激活码格式
                        </button>
                    </div>
                </form>

                <!-- 离线激活选项 -->
                <div class="offline-activation">
                    <p class="text-body">
                        无法在线激活？
                        <a href="#" class="offline-link" id="offlineActivationLink">
                            使用离线激活
                        </a>
                    </p>
                    <p class="help-text">
                        离线激活需要将设备指纹发送给技术支持，获取离线激活文件后导入激活。
                    </p>
                </div>

                <!-- 帮助信息 -->
                <div class="help-text" style="margin-top: var(--spacing-lg);">
                    如需技术支持，请联系：<EMAIL><br>
                    或拨打技术支持热线：400-888-0000
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/utils/fingerprint.js"></script>
    <script src="../js/components/license-manager.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeActivationPage();
        });

        function initializeActivationPage() {
            // 生成设备指纹
            generateDeviceFingerprint();
            
            // 绑定事件监听器
            bindEventListeners();
            
            // 检查现有激活状态
            checkExistingActivation();
        }

        function generateDeviceFingerprint() {
            // 模拟设备指纹生成
            setTimeout(() => {
                const fingerprint = DeviceFingerprint.generate();
                document.getElementById('deviceFingerprint').textContent = fingerprint;
            }, 1000);
        }

        function bindEventListeners() {
            const form = document.getElementById('activationForm');
            const activationCode = document.getElementById('activationCode');
            const validateBtn = document.getElementById('validateBtn');
            const offlineLink = document.getElementById('offlineActivationLink');

            // 表单提交
            form.addEventListener('submit', handleActivation);
            
            // 激活码输入格式化
            activationCode.addEventListener('input', formatActivationCode);
            
            // 验证按钮
            validateBtn.addEventListener('click', validateActivationCode);
            
            // 离线激活链接
            offlineLink.addEventListener('click', showOfflineActivation);
        }

        function formatActivationCode(event) {
            let value = event.target.value.replace(/[^A-Z0-9]/g, '');
            let formatted = '';
            
            for (let i = 0; i < value.length; i++) {
                if (i > 0 && i % 5 === 0) {
                    formatted += '-';
                }
                formatted += value[i];
            }
            
            event.target.value = formatted;
        }

        function validateActivationCode() {
            const code = document.getElementById('activationCode').value;
            const isValid = LicenseManager.validateCodeFormat(code);
            
            updateActivationStatus(
                isValid ? 'success' : 'error',
                isValid ? '激活码格式正确' : '激活码格式错误'
            );
        }

        function handleActivation(event) {
            event.preventDefault();
            
            const code = document.getElementById('activationCode').value;
            const fingerprint = document.getElementById('deviceFingerprint').textContent;
            
            if (!code) {
                updateActivationStatus('error', '请输入激活码');
                return;
            }
            
            updateActivationStatus('loading', '正在验证激活码...');
            
            // 模拟激活过程
            LicenseManager.activate(code, fingerprint)
                .then(result => {
                    if (result.success) {
                        updateActivationStatus('success', '激活成功！正在跳转...');
                        setTimeout(() => {
                            window.location.href = '../index.html';
                        }, 2000);
                    } else {
                        updateActivationStatus('error', result.message || '激活失败');
                    }
                })
                .catch(error => {
                    updateActivationStatus('error', '激活过程中发生错误');
                });
        }

        function updateActivationStatus(type, message) {
            const statusElement = document.getElementById('activationStatus');
            const statusDot = statusElement.querySelector('.status-dot');
            const statusText = statusElement.querySelector('span');
            
            // 移除所有状态类
            statusElement.className = 'activation-status ' + type;
            statusDot.className = 'status-dot ' + (type === 'success' ? 'online' : 
                                                   type === 'error' ? 'error' : 
                                                   type === 'loading' ? 'warning' : 'offline');
            statusText.textContent = message;
        }

        function checkExistingActivation() {
            // 检查是否已经激活
            if (LicenseManager.isActivated()) {
                updateActivationStatus('success', '软件已激活');
                document.getElementById('activateBtn').textContent = '进入软件';
                document.getElementById('activateBtn').onclick = () => {
                    window.location.href = '../index.html';
                };
            }
        }

        function showOfflineActivation(event) {
            event.preventDefault();
            alert('离线激活功能开发中...\n\n请将设备指纹发送给技术支持获取离线激活文件。');
        }
    </script>
</body>
</html>
