/**
 * Microsoft Fluent Design System for AirMonitor
 * 商用空调调试监控软件专用设计系统
 * 
 * 设计规范:
 * - 主色调: #FF005FB8 (深蓝色)
 * - 最低分辨率支持: 1280×768
 * - 专业工具软件界面风格
 * - Microsoft Fluent Design语言
 */

/* ================================
   CSS自定义属性 (设计令牌)
   ================================ */

:root {
  /* 主色系 - Primary Colors */
  --color-primary: #005FB8;
  --color-primary-light: #0078D4;
  --color-primary-dark: #004578;
  --color-primary-hover: #106EBE;
  --color-primary-pressed: #005A9E;
  --color-primary-disabled: #A6A6A6;

  /* 功能色系 - Semantic Colors */
  --color-success: #107C10;
  --color-success-light: #54B054;
  --color-success-dark: #0E5A0E;
  
  --color-warning: #FF8C00;
  --color-warning-light: #FFB84D;
  --color-warning-dark: #CC7000;
  
  --color-error: #D13438;
  --color-error-light: #E74856;
  --color-error-dark: #A4262C;
  
  --color-info: #0078D4;
  --color-info-light: #40E0FF;
  --color-info-dark: #005A9E;

  /* 中性色系 - Neutral Colors */
  --color-neutral-100: #FFFFFF;
  --color-neutral-95: #F9F9F9;
  --color-neutral-90: #F3F2F1;
  --color-neutral-80: #EDEBE9;
  --color-neutral-70: #E1DFDD;
  --color-neutral-60: #D2D0CE;
  --color-neutral-50: #C8C6C4;
  --color-neutral-40: #A19F9D;
  --color-neutral-30: #8A8886;
  --color-neutral-20: #605E5C;
  --color-neutral-10: #323130;
  --color-neutral-0: #000000;

  /* 背景色系 - Background Colors */
  --bg-primary: var(--color-neutral-100);
  --bg-secondary: var(--color-neutral-95);
  --bg-tertiary: var(--color-neutral-90);
  --bg-card: var(--color-neutral-100);
  --bg-overlay: rgba(0, 0, 0, 0.4);
  --bg-acrylic: rgba(243, 242, 241, 0.8);

  /* 文字色系 - Text Colors */
  --text-primary: var(--color-neutral-10);
  --text-secondary: var(--color-neutral-20);
  --text-tertiary: var(--color-neutral-30);
  --text-disabled: var(--color-neutral-40);
  --text-on-primary: var(--color-neutral-100);
  --text-on-dark: var(--color-neutral-100);

  /* 边框色系 - Border Colors */
  --border-primary: var(--color-neutral-60);
  --border-secondary: var(--color-neutral-70);
  --border-tertiary: var(--color-neutral-80);
  --border-focus: var(--color-primary);
  --border-error: var(--color-error);

  /* 阴影系统 - Shadow System */
  --shadow-2: 0 1px 2px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
  --shadow-4: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
  --shadow-8: 0 4px 8px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
  --shadow-16: 0 8px 16px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);
  --shadow-64: 0 32px 64px rgba(0, 0, 0, 0.14), 0 0 2px rgba(0, 0, 0, 0.12);

  /* 圆角系统 - Border Radius */
  --radius-small: 2px;
  --radius-medium: 4px;
  --radius-large: 8px;
  --radius-xlarge: 12px;

  /* 间距系统 - Spacing System (8px网格) */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 字体系统 - Typography */
  --font-family-primary: "Microsoft YaHei UI", "Segoe UI", "PingFang SC", "Hiragino Sans GB", sans-serif;
  --font-family-mono: "Consolas", "Monaco", "Courier New", monospace;

  /* 字体大小 - Font Sizes */
  --font-size-caption: 12px;
  --font-size-body: 14px;
  --font-size-body-large: 16px;
  --font-size-subtitle: 18px;
  --font-size-title: 24px;
  --font-size-display: 32px;

  /* 行高 - Line Heights */
  --line-height-caption: 16px;
  --line-height-body: 20px;
  --line-height-body-large: 24px;
  --line-height-subtitle: 28px;
  --line-height-title: 32px;
  --line-height-display: 40px;

  /* 字重 - Font Weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 动画系统 - Animation System */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --easing-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0.0, 0.0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0.0, 1, 1);

  /* Z-Index层级 - Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* 布局断点 - Breakpoints */
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1440px;
  --breakpoint-xxl: 1920px;

  /* 专业工具软件特定变量 */
  --toolbar-height: 48px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 48px;
  --status-bar-height: 24px;
  --panel-min-width: 320px;
}

/* ================================
   基础重置和全局样式
   ================================ */

* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
}

/* 滚动条样式 - Fluent Design风格 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-neutral-50);
  border-radius: 6px;
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-neutral-40);
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* ================================
   字体排版系统
   ================================ */

.text-caption {
  font-size: var(--font-size-caption);
  line-height: var(--line-height-caption);
  font-weight: var(--font-weight-regular);
}

.text-body {
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  font-weight: var(--font-weight-regular);
}

.text-body-large {
  font-size: var(--font-size-body-large);
  line-height: var(--line-height-body-large);
  font-weight: var(--font-weight-regular);
}

.text-subtitle {
  font-size: var(--font-size-subtitle);
  line-height: var(--line-height-subtitle);
  font-weight: var(--font-weight-medium);
}

.text-title {
  font-size: var(--font-size-title);
  line-height: var(--line-height-title);
  font-weight: var(--font-weight-semibold);
}

.text-display {
  font-size: var(--font-size-display);
  line-height: var(--line-height-display);
  font-weight: var(--font-weight-bold);
}

/* 文字颜色工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

/* 字重工具类 */
.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* ================================
   间距工具类
   ================================ */

.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 方向性间距 */
.mt-sm { margin-top: var(--spacing-sm); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.ml-sm { margin-left: var(--spacing-sm); }
.mr-sm { margin-right: var(--spacing-sm); }

.pt-sm { padding-top: var(--spacing-sm); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pl-sm { padding-left: var(--spacing-sm); }
.pr-sm { padding-right: var(--spacing-sm); }

/* ================================
   Fluent Design 核心组件样式
   ================================ */

/* 按钮组件 - Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-body);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-standard);
  user-select: none;
  white-space: nowrap;
  min-height: 32px;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 主要按钮 */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-primary:active:not(:disabled) {
  background-color: var(--color-primary-pressed);
  border-color: var(--color-primary-pressed);
}

/* 次要按钮 */
.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
}

/* 默认按钮 */
.btn-default {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-default:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

/* 按钮尺寸 */
.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-caption);
  min-height: 24px;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-body-large);
  min-height: 40px;
}

/* 卡片组件 - Card Components */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-tertiary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-2);
  overflow: hidden;
  transition: all var(--duration-normal) var(--easing-standard);
}

.card:hover {
  box-shadow: var(--shadow-4);
  border-color: var(--border-secondary);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-tertiary);
  background-color: var(--bg-secondary);
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-tertiary);
  background-color: var(--bg-secondary);
}

/* 输入框组件 - Input Components */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: all var(--duration-fast) var(--easing-standard);
  min-height: 32px;
}

.input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 1px var(--border-focus);
}

.input:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.input.error {
  border-color: var(--border-error);
}

.input.error:focus {
  border-color: var(--border-error);
  box-shadow: 0 0 0 1px var(--border-error);
}

/* 表单组件 - Form Components */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.form-error {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-caption);
  color: var(--color-error);
}

/* 选择框组件 - Select Components */
.select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.select-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-medium);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  cursor: pointer;
  appearance: none;
  min-height: 32px;
}

.select::after {
  content: '';
  position: absolute;
  top: 50%;
  right: var(--spacing-md);
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--text-secondary);
  pointer-events: none;
}

/* 复选框和单选框 - Checkbox & Radio */
.checkbox, .radio {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  user-select: none;
}

.checkbox-input, .radio-input {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
  cursor: pointer;
  position: relative;
  transition: all var(--duration-fast) var(--easing-standard);
}

.checkbox-input {
  border-radius: var(--radius-small);
}

.radio-input {
  border-radius: 50%;
}

.checkbox-input:checked, .radio-input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-input:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-on-primary);
  font-size: 10px;
  font-weight: bold;
}

.radio-input:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--text-on-primary);
}

/* 开关组件 - Toggle Switch */
.toggle {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  user-select: none;
}

.toggle-switch {
  width: 40px;
  height: 20px;
  background-color: var(--color-neutral-60);
  border-radius: 10px;
  position: relative;
  transition: background-color var(--duration-normal) var(--easing-standard);
  cursor: pointer;
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: var(--bg-primary);
  border-radius: 50%;
  transition: transform var(--duration-normal) var(--easing-standard);
  box-shadow: var(--shadow-2);
}

.toggle-input:checked + .toggle-switch {
  background-color: var(--color-primary);
}

.toggle-input:checked + .toggle-switch::after {
  transform: translateX(20px);
}

.toggle-input {
  display: none;
}

/* 进度条组件 - Progress Components */
.progress {
  width: 100%;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-small);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-small);
  transition: width var(--duration-normal) var(--easing-standard);
}

.progress-bar.success {
  background-color: var(--color-success);
}

.progress-bar.warning {
  background-color: var(--color-warning);
}

.progress-bar.error {
  background-color: var(--color-error);
}

/* 徽章组件 - Badge Components */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--color-primary);
  color: var(--text-on-primary);
}

.badge-success {
  background-color: var(--color-success);
  color: var(--text-on-primary);
}

.badge-warning {
  background-color: var(--color-warning);
  color: var(--text-on-primary);
}

.badge-error {
  background-color: var(--color-error);
  color: var(--text-on-primary);
}

.badge-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

/* 状态指示器 - Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-body);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.online {
  background-color: var(--color-success);
  box-shadow: 0 0 0 2px rgba(16, 124, 16, 0.2);
}

.status-dot.offline {
  background-color: var(--color-neutral-40);
}

.status-dot.error {
  background-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(209, 52, 56, 0.2);
}

.status-dot.warning {
  background-color: var(--color-warning);
  box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.2);
}
