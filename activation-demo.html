<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirMonitor 激活页面演示</title>
    <style>
        body {
            font-family: "Microsoft YaHei UI", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: #005FB8;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-content {
            padding: 20px;
        }
        
        .demo-info {
            background: #f0f8ff;
            border: 1px solid #005FB8;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .demo-codes {
            background: #fff9e6;
            border: 1px solid #ff8c00;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .demo-codes h3 {
            margin-top: 0;
            color: #cc7000;
        }
        
        .code-item {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            font-family: monospace;
            border-left: 4px solid #ff8c00;
        }
        
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            height: 700px;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #005FB8;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #004578;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>AirMonitor 激活页面演示 - 横版布局</h1>
            <p>商用空调调试监控软件 - Microsoft Fluent Design System</p>
        </div>
        
        <div class="demo-content">
            <div class="demo-info">
                <h3>🎯 功能特性 - 全新横版布局</h3>
                <ul>
                    <li><strong>横版专业布局</strong>：左侧品牌展示，右侧激活表单，更符合PC端使用习惯</li>
                    <li><strong>品牌展示区域</strong>：动态背景效果，突出软件专业性和品牌形象</li>
                    <li><strong>设备指纹识别</strong>：自动生成基于硬件信息的唯一设备指纹</li>
                    <li><strong>激活码验证</strong>：支持格式化输入和实时验证</li>
                    <li><strong>多种License类型</strong>：试用版、标准版、专业版、企业版</li>
                    <li><strong>权限管理</strong>：基于License类型的功能权限控制</li>
                    <li><strong>状态反馈</strong>：实时显示激活状态和进度</li>
                    <li><strong>响应式设计</strong>：桌面横版，移动端自动切换为竖版布局</li>
                </ul>
            </div>
            
            <div class="demo-codes">
                <h3>🔑 测试激活码</h3>
                <p>您可以使用以下预设的激活码进行测试：</p>
                
                <div class="code-item">
                    <strong>试用版 (30天)：</strong><br>
                    <code>TRIAL-00000-00000-00000-00001</code>
                </div>
                
                <div class="code-item">
                    <strong>标准版 (1年)：</strong><br>
                    <code>STAND-12345-67890-ABCDE-FGHIJ</code>
                </div>
                
                <div class="code-item">
                    <strong>专业版 (1年)：</strong><br>
                    <code>PROFE-SSION-AL123-45678-90ABC</code>
                </div>
                
                <div class="code-item">
                    <strong>企业版 (永久)：</strong><br>
                    <code>ENTER-PRISE-12345-67890-ABCDE</code>
                </div>
            </div>
            
            <div style="text-align: center; margin-bottom: 20px;">
                <a href="pages/activation.html" class="btn" target="_blank">在新窗口中打开</a>
                <a href="#" class="btn" onclick="document.getElementById('activationFrame').src = 'pages/activation.html'">刷新页面</a>
            </div>
            
            <div class="iframe-container">
                <iframe id="activationFrame" src="pages/activation.html"></iframe>
            </div>
            
            <div style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 4px;">
                <h3>📋 使用说明</h3>
                <ol>
                    <li>页面加载后会自动生成设备指纹</li>
                    <li>在激活码输入框中输入上述任一测试激活码</li>
                    <li>点击"验证激活码格式"按钮检查格式是否正确</li>
                    <li>点击"激活软件"按钮进行激活</li>
                    <li>激活成功后会显示成功状态并自动跳转</li>
                </ol>
                
                <h3>🔧 技术实现</h3>
                <ul>
                    <li><strong>横版布局设计</strong>：使用Flexbox实现左右分栏布局，提升专业感</li>
                    <li><strong>动态背景效果</strong>：CSS动画和渐变背景，增强视觉吸引力</li>
                    <li><strong>设备指纹</strong>：基于Canvas、WebGL、音频等多种技术生成</li>
                    <li><strong>激活验证</strong>：本地模拟验证，实际项目中会调用服务器API</li>
                    <li><strong>数据存储</strong>：使用localStorage保存激活信息</li>
                    <li><strong>权限控制</strong>：基于License类型分配不同功能权限</li>
                    <li><strong>响应式适配</strong>：大屏横版，小屏自动切换竖版布局</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
