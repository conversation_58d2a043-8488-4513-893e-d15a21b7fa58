<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirMonitor - Fluent Design System 演示</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .demo-section {
            margin-bottom: var(--spacing-xxl);
            padding: var(--spacing-lg);
            background-color: var(--bg-card);
            border-radius: var(--radius-large);
            border: 1px solid var(--border-tertiary);
        }
        
        .demo-title {
            font-size: var(--font-size-title);
            font-weight: var(--font-weight-semibold);
            color: var(--color-primary);
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 2px solid var(--color-primary);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .demo-item {
            padding: var(--spacing-md);
            background-color: var(--bg-secondary);
            border-radius: var(--radius-medium);
            border: 1px solid var(--border-tertiary);
        }
        
        .demo-item h4 {
            margin: 0 0 var(--spacing-sm) 0;
            font-size: var(--font-size-body-large);
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="app-header">
            <div class="app-header-left">
                <div class="app-logo">
                    <div class="app-logo-icon">A</div>
                    <span>AirMonitor Design System</span>
                </div>
            </div>
            <div class="app-header-center">
                <div class="connection-status">
                    <div class="connection-indicator connected"></div>
                    <span class="connection-text">设计系统演示</span>
                    <span class="connection-details">Fluent Design</span>
                </div>
            </div>
            <div class="app-header-right">
                <button class="btn btn-primary btn-small">预览模式</button>
            </div>
        </header>

        <main class="app-main">
            <div class="app-content">
                <div class="app-content-main">
                    <!-- 色彩系统演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">色彩系统 Color System</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>主色调</h4>
                                <div style="display: flex; gap: var(--spacing-sm); margin-top: var(--spacing-sm);">
                                    <div style="width: 40px; height: 40px; background-color: var(--color-primary); border-radius: var(--radius-medium);"></div>
                                    <div style="width: 40px; height: 40px; background-color: var(--color-primary-light); border-radius: var(--radius-medium);"></div>
                                    <div style="width: 40px; height: 40px; background-color: var(--color-primary-dark); border-radius: var(--radius-medium);"></div>
                                </div>
                                <p class="text-caption">主蓝色 #005FB8</p>
                            </div>
                            <div class="demo-item">
                                <h4>功能色彩</h4>
                                <div style="display: flex; gap: var(--spacing-sm); margin-top: var(--spacing-sm);">
                                    <div style="width: 40px; height: 40px; background-color: var(--color-success); border-radius: var(--radius-medium);"></div>
                                    <div style="width: 40px; height: 40px; background-color: var(--color-warning); border-radius: var(--radius-medium);"></div>
                                    <div style="width: 40px; height: 40px; background-color: var(--color-error); border-radius: var(--radius-medium);"></div>
                                    <div style="width: 40px; height: 40px; background-color: var(--color-info); border-radius: var(--radius-medium);"></div>
                                </div>
                                <p class="text-caption">成功 | 警告 | 错误 | 信息</p>
                            </div>
                        </div>
                    </section>

                    <!-- 字体系统演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">字体系统 Typography</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>字体层级</h4>
                                <div class="text-display">显示文字 32px</div>
                                <div class="text-title">标题文字 24px</div>
                                <div class="text-subtitle">副标题 18px</div>
                                <div class="text-body-large">大正文 16px</div>
                                <div class="text-body">正文 14px</div>
                                <div class="text-caption">说明文字 12px</div>
                            </div>
                            <div class="demo-item">
                                <h4>字重变化</h4>
                                <div class="text-body font-regular">常规字重 400</div>
                                <div class="text-body font-medium">中等字重 500</div>
                                <div class="text-body font-semibold">半粗字重 600</div>
                                <div class="text-body font-bold">粗体字重 700</div>
                            </div>
                        </div>
                    </section>

                    <!-- 按钮组件演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">按钮组件 Buttons</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>按钮类型</h4>
                                <div style="display: flex; gap: var(--spacing-sm); flex-wrap: wrap; margin-top: var(--spacing-sm);">
                                    <button class="btn btn-primary">主要按钮</button>
                                    <button class="btn btn-secondary">次要按钮</button>
                                    <button class="btn btn-default">默认按钮</button>
                                    <button class="btn btn-primary" disabled>禁用按钮</button>
                                </div>
                            </div>
                            <div class="demo-item">
                                <h4>按钮尺寸</h4>
                                <div style="display: flex; gap: var(--spacing-sm); align-items: center; flex-wrap: wrap; margin-top: var(--spacing-sm);">
                                    <button class="btn btn-primary btn-small">小按钮</button>
                                    <button class="btn btn-primary">标准按钮</button>
                                    <button class="btn btn-primary btn-large">大按钮</button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 表单组件演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">表单组件 Form Controls</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>输入框</h4>
                                <div class="form-group">
                                    <label class="form-label">设备名称</label>
                                    <input type="text" class="input" placeholder="请输入设备名称" value="空调主机-01">
                                    <div class="form-help">设备的唯一标识名称</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">错误示例</label>
                                    <input type="text" class="input error" placeholder="错误状态" value="无效输入">
                                    <div class="form-error">请输入有效的设备名称</div>
                                </div>
                            </div>
                            <div class="demo-item">
                                <h4>选择框和开关</h4>
                                <div class="form-group">
                                    <label class="form-label">设备类型</label>
                                    <div class="select">
                                        <select class="select-input">
                                            <option>中央空调</option>
                                            <option>分体空调</option>
                                            <option>多联机</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="toggle">
                                        <input type="checkbox" class="toggle-input" checked>
                                        <div class="toggle-switch"></div>
                                        <span>启用自动监控</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 专业组件演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">专业组件 Professional Components</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>参数监控卡片</h4>
                                <div class="parameter-card">
                                    <div class="parameter-header">
                                        <span class="parameter-name">进水温度</span>
                                        <span class="parameter-unit">°C</span>
                                    </div>
                                    <div class="parameter-value">23.5</div>
                                    <div class="parameter-trend up">
                                        <span>↗</span>
                                        <span>+0.3°C</span>
                                    </div>
                                </div>
                            </div>
                            <div class="demo-item">
                                <h4>连接状态指示</h4>
                                <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                                    <div class="connection-status">
                                        <div class="connection-indicator connected"></div>
                                        <span class="connection-text">已连接</span>
                                        <span class="connection-details">COM3</span>
                                    </div>
                                    <div class="connection-status">
                                        <div class="connection-indicator connecting"></div>
                                        <span class="connection-text">连接中</span>
                                        <span class="connection-details">COM4</span>
                                    </div>
                                    <div class="connection-status">
                                        <div class="connection-indicator error"></div>
                                        <span class="connection-text">连接失败</span>
                                        <span class="connection-details">超时</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 状态和反馈演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">状态反馈 Status & Feedback</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>告警组件</h4>
                                <div class="alert alert-success">
                                    <div class="alert-content">
                                        <div class="alert-title">连接成功</div>
                                        <div class="alert-message">设备已成功连接，可以开始监控。</div>
                                    </div>
                                </div>
                                <div class="alert alert-warning">
                                    <div class="alert-content">
                                        <div class="alert-title">参数异常</div>
                                        <div class="alert-message">温度超出正常范围，请检查设备状态。</div>
                                    </div>
                                </div>
                            </div>
                            <div class="demo-item">
                                <h4>状态徽章</h4>
                                <div style="display: flex; gap: var(--spacing-sm); flex-wrap: wrap;">
                                    <span class="badge badge-success">正常</span>
                                    <span class="badge badge-warning">警告</span>
                                    <span class="badge badge-error">故障</span>
                                    <span class="badge badge-primary">运行中</span>
                                    <span class="badge badge-secondary">离线</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 进度和加载演示 -->
                    <section class="demo-section">
                        <h2 class="demo-title">进度指示 Progress Indicators</h2>
                        <div class="demo-grid">
                            <div class="demo-item">
                                <h4>进度条</h4>
                                <div style="margin-bottom: var(--spacing-sm);">
                                    <div class="progress">
                                        <div class="progress-bar" style="width: 75%;"></div>
                                    </div>
                                    <p class="text-caption" style="margin-top: var(--spacing-xs);">数据同步进度: 75%</p>
                                </div>
                                <div>
                                    <div class="progress">
                                        <div class="progress-bar warning" style="width: 45%;"></div>
                                    </div>
                                    <p class="text-caption" style="margin-top: var(--spacing-xs);">系统负载: 45%</p>
                                </div>
                            </div>
                            <div class="demo-item">
                                <h4>加载指示器</h4>
                                <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                    <div class="loading-spinner"></div>
                                    <span class="text-body">加载中...</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-top: var(--spacing-md);">
                                    <div class="loading-spinner large"></div>
                                    <span class="text-body">大尺寸加载器</span>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="app-status-bar">
            <div class="app-status-left">
                <span>设计系统版本: v1.0.0</span>
                <span>|</span>
                <span>Microsoft Fluent Design</span>
            </div>
            <div class="app-status-right">
                <span>分辨率: 1280×768+</span>
                <span>|</span>
                <span>主题: 专业工具</span>
            </div>
        </footer>
    </div>
</body>
</html>
