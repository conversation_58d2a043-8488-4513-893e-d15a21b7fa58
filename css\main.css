/**
 * AirMonitor 主样式文件
 * 商用空调调试监控软件 - Microsoft Fluent Design System
 * 
 * 样式导入顺序:
 * 1. 设计系统基础 (Fluent Design)
 * 2. 布局组件
 * 3. 专业工具组件
 * 4. 页面特定样式
 */

/* 导入设计系统基础 */
@import url('./themes/fluent-design.css');

/* 导入组件样式 */
@import url('./components/layout.css');
@import url('./components/professional.css');

/* ================================
   应用程序特定样式
   ================================ */

/* 应用程序根容器 */
#app {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  font-family: var(--font-family-primary);
  background-color: var(--bg-primary);
}

/* ================================
   页面过渡动画
   ================================ */

.page-transition {
  transition: all var(--duration-normal) var(--easing-standard);
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-20px);
}

/* ================================
   模态框和弹窗
   ================================ */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal-backdrop);
  animation: fadeIn var(--duration-normal) var(--easing-standard);
}

.modal {
  background-color: var(--bg-card);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-64);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn var(--duration-normal) var(--easing-decelerate);
}

.modal-header {
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: var(--radius-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all var(--duration-fast) var(--easing-standard);
}

.modal-close:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  border-top: 1px solid var(--border-tertiary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ================================
   提示框和工具提示
   ================================ */

.tooltip {
  position: absolute;
  background-color: var(--color-neutral-10);
  color: var(--text-on-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-caption);
  white-space: nowrap;
  z-index: var(--z-tooltip);
  box-shadow: var(--shadow-8);
  animation: tooltipIn var(--duration-fast) var(--easing-standard);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--color-neutral-10);
}

@keyframes tooltipIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ================================
   加载状态
   ================================ */

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-tertiary);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.large {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

/* ================================
   空状态
   ================================ */

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
  color: var(--text-secondary);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--font-size-body-large);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.empty-state-description {
  font-size: var(--font-size-body);
  margin-bottom: var(--spacing-md);
  max-width: 400px;
}

/* ================================
   响应式工具类
   ================================ */

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ================================
   打印样式
   ================================ */

@media print {
  .app-sidebar,
  .app-header,
  .app-status-bar,
  .toolbar,
  .btn,
  .modal-overlay {
    display: none !important;
  }
  
  .app-content {
    margin: 0;
    padding: 0;
  }
  
  .card,
  .panel {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* ================================
   高对比度模式支持
   ================================ */

@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-primary: #000000;
    --text-secondary: #000000;
  }
  
  .btn {
    border-width: 2px;
  }
  
  .card,
  .panel {
    border-width: 2px;
  }
}

/* ================================
   减少动画模式支持
   ================================ */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
